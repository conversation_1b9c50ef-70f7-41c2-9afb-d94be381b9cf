### Retrieve chunks - basic request
POST {{dev25}}/api/knowledge-hub/retrieve
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
  "query": "Что ты умеешь?"
}

###

### Retrieve chunks - with history
POST {{dev25}}/api/knowledge-hub/retrieve
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
  "query": "Расскажи подробнее",
  "history": [
    {
      "role": "user",
      "content": "Что ты умеешь?"
    },
    {
      "role": "assistant",
      "content": "Я могу помочь вам найти информацию в базе знаний."
    }
  ]
}

###

### Retrieve chunks - with settings
POST {{dev25}}/api/knowledge-hub/retrieve
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
  "query": "Как настроить интеграцию?",
  "settings": {
    "pipeline": "HYBRID",
    "search": {
      "topK": 10,
      "scoreThreshold": 0.5,
      "segments": ["documentation", "guides"]
    },
    "llm": {
      "model": "gpt-4o",
      "temperature": 0.1
    }
  }
}

###

### Retrieve chunks - full example with history and settings
POST {{dev25}}/api/knowledge-hub/retrieve
Authorization: Bearer {{apiKey}}
Content-Type: application/json

{
  "query": "Покажи примеры использования API",
  "history": [
    {
      "role": "user",
      "content": "Как работать с API?"
    },
    {
      "role": "assistant",
      "content": "API предоставляет несколько эндпоинтов для работы с базой знаний."
    },
    {
      "role": "user",
      "content": "Какие есть методы аутентификации?"
    },
    {
      "role": "assistant",
      "content": "Используется Bearer токен для аутентификации."
    }
  ],
  "settings": {
    "pipeline": "SEMANTIC",
    "search": {
      "topK": 5,
      "scoreThreshold": 0.7,
      "segments": ["api-docs"]
    },
    "llm": {
      "model": "gpt-4o-mini",
      "temperature": 0.0
    }
  }
}

###
