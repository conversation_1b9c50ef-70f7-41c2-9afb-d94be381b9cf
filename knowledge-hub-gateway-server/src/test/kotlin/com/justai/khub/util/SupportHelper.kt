package com.justai.khub.util

import com.justai.khub.api.fe.model.CorporateTariffRequestDTO
import com.justai.khub.common.util.JSON
import com.justai.khub.support.enumeration.RequestStatus
import com.justai.khub.support.repository.CorporateTariffRequestRepository
import com.justai.security.session.auth.JustSessionAuthentication
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@Component
class SupportHelper {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var corporateTariffRequestRepository: CorporateTariffRequestRepository

    fun requestCorporateTariff(
        request: CorporateTariffRequestDTO,
        auth: JustSessionAuthentication = AuthUtils.mockCCUser()
    ) {
        mockMvc.perform(
            MockMvcRequestBuilders.post("/api/khub/support/requestCorporateTariff")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.stringify(request))
                .with(SecurityMockMvcRequestPostProcessors.authentication(auth))
        ).andExpect(MockMvcResultMatchers.status().isOk)
    }

    fun findAllCorporateTariffRequests() = corporateTariffRequestRepository.findAll()

    fun findCorporateTariffRequestsByStatus(status: RequestStatus) =
        corporateTariffRequestRepository.findByStatusInOrderByCreatedAtAsc(setOf(status))

    fun createTestCorporateTariffRequest(
        nameAndSurname: String = "Test User",
        companyName: String = "Test Company",
        industry: String = "Test Industry",
        workPhone: String = "+1234567890",
        workEmail: String = "<EMAIL>",
        comment: String? = "Test comment"
    ): CorporateTariffRequestDTO {
        return CorporateTariffRequestDTO(
            nameAndSurname = nameAndSurname,
            companyName = companyName,
            industry = industry,
            workPhone = workPhone,
            workEmail = workEmail,
            comment = comment
        )
    }
}
