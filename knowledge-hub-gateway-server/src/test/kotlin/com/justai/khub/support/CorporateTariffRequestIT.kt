package com.justai.khub.support

import com.justai.khub.BaseIT
import com.justai.khub.support.enumeration.RequestStatus
import com.justai.khub.support.job.CorporateTariffRequestProcessingJob
import com.justai.khub.support.repository.CorporateTariffRequestRepository
import com.justai.khub.support.service.CorporateTariffRequestMailService
import com.justai.khub.util.SupportHelper
import jakarta.mail.internet.MimeMessage
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.mail.javamail.JavaMailSender

class CorporateTariffRequestIT : BaseIT() {

    @Autowired
    private lateinit var supportHelper: SupportHelper

    @Autowired
    private lateinit var javaMailSender: JavaMailSender

    @SpyBean
    private lateinit var corporateTariffRequestMailService: CorporateTariffRequestMailService

    @Autowired
    private lateinit var corporateTariffRequestProcessingJob: CorporateTariffRequestProcessingJob

    @Autowired
    private lateinit var corporateTariffRequestRepository: CorporateTariffRequestRepository

    @AfterEach
    fun cleanup() {
        corporateTariffRequestRepository.deleteAll()
    }

    @Test
    fun testCreateCorporateTariffRequest() {
        // Create a test request
        val request = supportHelper.createTestCorporateTariffRequest()

        // Submit the request
        supportHelper.requestCorporateTariff(request)

        // Verify the request was saved
        val savedRequests = supportHelper.findAllCorporateTariffRequests()
        assertEquals(1, savedRequests.size)

        val savedRequest = savedRequests.first()
        assertEquals(request.nameAndSurname, savedRequest.nameAndSurname)
        assertEquals(request.companyName, savedRequest.companyName)
        assertEquals(request.industry, savedRequest.industry)
        assertEquals(request.workPhone, savedRequest.workPhone)
        assertEquals(request.workEmail, savedRequest.workEmail)
        assertEquals(request.comment, savedRequest.comment)
        assertEquals(RequestStatus.PENDING, savedRequest.status)
    }

    @Test
    fun testProcessCorporateTariffRequest() {
        // Create and submit a test request
        val request = supportHelper.createTestCorporateTariffRequest()
        supportHelper.requestCorporateTariff(request)

        // Verify the request is in PENDING status
        val pendingRequests = supportHelper.findCorporateTariffRequestsByStatus(RequestStatus.PENDING)
        assertEquals(1, pendingRequests.size)

        // Reset the mail sender to verify it's called
        Mockito.clearInvocations(javaMailSender)

        // Process the request
        corporateTariffRequestProcessingJob.processPendingCorporateRequests()

        // Verify the email was sent
        Mockito.verify(javaMailSender).createMimeMessage()
        Mockito.verify(javaMailSender).send(Mockito.any(MimeMessage::class.java))

        // Verify the request is now in SENT status
        val sentRequests = supportHelper.findCorporateTariffRequestsByStatus(RequestStatus.SENT)
        assertEquals(1, sentRequests.size)
    }

    @Test
    fun testCreateMultipleCorporateTariffRequests() {
        // Create and submit multiple test requests
        val request1 = supportHelper.createTestCorporateTariffRequest(
            nameAndSurname = "User 1",
            companyName = "Company 1",
            workEmail = "<EMAIL>"
        )
        val request2 = supportHelper.createTestCorporateTariffRequest(
            nameAndSurname = "User 2",
            companyName = "Company 2",
            workEmail = "<EMAIL>"
        )

        supportHelper.requestCorporateTariff(request1)
        supportHelper.requestCorporateTariff(request2)

        // Verify both requests were saved
        val savedRequests = supportHelper.findAllCorporateTariffRequests()
        assertEquals(2, savedRequests.size)

        // Process the requests
        corporateTariffRequestProcessingJob.processPendingCorporateRequests()

        // Verify both requests are now in SENT status
        val sentRequests = supportHelper.findCorporateTariffRequestsByStatus(RequestStatus.SENT)
        assertEquals(2, sentRequests.size)
    }
}
