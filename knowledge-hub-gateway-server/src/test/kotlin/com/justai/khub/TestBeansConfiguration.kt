package com.justai.khub

import com.justai.khub.attachment.job.ExpiredLinksCleanupJob
import com.justai.khub.attachment.service.AttachmentService
import com.justai.khub.billing.AnalyticsServiceGrpc
import com.justai.khub.billing.InstructionServiceGrpc
import com.justai.khub.billing.job.ProcessStorageBillingJob
import com.justai.khub.billing.job.RollbackOutdatedOperationsJob
import com.justai.khub.channel.dto.BotProjectReadDto
import com.justai.khub.common.configuration.dto.IntegrationProperties
import com.justai.khub.common.connector.*
import com.justai.khub.common.job.TimedOutEntitiesReleaseJob
import com.justai.khub.mock.MockAnalyticsGrpcService
import com.justai.khub.mock.MockBillingGrpcService
import com.justai.khub.mock.MockRagServiceConnector
import com.justai.khub.project.mapper.RagServiceMapper
import com.justai.khub.qa.job.TestSetProcessingJob
import com.justai.khub.qa.job.TestSetScheduleProcessingJob
import com.justai.khub.util.AuthUtils
import com.justai.khub.util.GrpcServerExtension
import com.justai.khub.util.JsonDefaultContentTypeMockMvcConfigurer
import com.justai.khub.util.TestDataFactory
import com.justai.loadbalancer.annotations.HttpInternal
import com.justai.onprem.license.KeyV1
import com.justai.security.session.auth.JustSessionAuthentication
import com.justai.zb.migrations.SpringMigrations
import io.lakefs.clients.sdk.ApiClient
import jakarta.annotation.PostConstruct
import org.apache.commons.lang3.RandomStringUtils
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.client.RestTemplate
import org.springframework.web.context.WebApplicationContext
import java.nio.file.Path


@TestConfiguration
class TestBeansConfiguration {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var wac: WebApplicationContext

    @PostConstruct
    fun init() {
        SpringMigrations.ofEnvironment(wac.environment).cleanMigrate()
    }

    @Bean
    fun grpcServerExtension(): GrpcServerExtension = GrpcServerExtension()

    @Bean
    fun mockBillingGrpcService(): InstructionServiceGrpc.InstructionServiceImplBase {
        return MockBillingGrpcService()
    }

    @Bean
    fun mockAnalyticsGrpcService(): AnalyticsServiceGrpc.AnalyticsServiceImplBase {
        return MockAnalyticsGrpcService()
    }

    @Bean
    @Primary
    fun billingServiceClient(
        grpcServerExtension: GrpcServerExtension,
        billingGrpcService: InstructionServiceGrpc.InstructionServiceImplBase
    ): InstructionServiceGrpc.InstructionServiceBlockingStub {
        val channel = grpcServerExtension.registerService("billingService", billingGrpcService)
        return InstructionServiceGrpc.newBlockingStub(channel)
    }

    @Bean
    @Primary
    fun analyticsServiceClient(
        grpcServerExtension: GrpcServerExtension,
        analyticsGrpcService: AnalyticsServiceGrpc.AnalyticsServiceImplBase
    ): AnalyticsServiceGrpc.AnalyticsServiceBlockingStub {
        val channel = grpcServerExtension.registerService("analyticsService", analyticsGrpcService)
        return AnalyticsServiceGrpc.newBlockingStub(channel)
    }

    @Bean
    fun defaultAuth(): JustSessionAuthentication {
        return AuthUtils.mockCCUser()
    }

    @Bean
    fun mockMvc(wac: WebApplicationContext): MockMvc {
        return MockMvcBuilders
            .webAppContextSetup(wac)
            .apply<DefaultMockMvcBuilder>(SecurityMockMvcConfigurers.springSecurity())
            .apply<DefaultMockMvcBuilder>(JsonDefaultContentTypeMockMvcConfigurer())
            .build()
    }

    @Bean
    @Primary
    fun lakefsConnector(
        lakeFSClient: ApiClient,
        integrationProperties: IntegrationProperties
    ): LakefsConnector {
        val spy = Mockito.spy(LakefsConnector(lakeFSClient, integrationProperties))
        Mockito.doAnswer { "repository-id-${RandomStringUtils.randomAlphabetic(10)}" }.`when`(spy).createRepository(any())
        Mockito.doAnswer { }.`when`(spy).deleteRepository(any())
        Mockito.doAnswer { }.`when`(spy).createBranch(any(), any())
        Mockito.doAnswer { }.`when`(spy).deleteFile(any(), any(), any())
        Mockito.doAnswer { }.`when`(spy).addFile(any(), any(), any(), any<Path>())
        Mockito.doReturn(byteArrayOf()).`when`(spy).getFileContent(any(), any(), any())
        return spy
    }

    @Bean
    @Primary
    fun ragServiceConnector(
        ragServiceMapper: RagServiceMapper,
        @HttpInternal ragServiceRestTemplate: RestTemplate,
        attachmentService: AttachmentService,
        integrationProperties: IntegrationProperties
    ): RagServiceConnector {
        log.info("Creating MockRagServiceConnector")
        return MockRagServiceConnector(ragServiceMapper, ragServiceRestTemplate, attachmentService, integrationProperties)
    }


    @Bean
    @Primary
    fun ingesterConnector(): IngesterConnector {
        val mock = Mockito.mock(IngesterConnector::class.java)
        Mockito.`when`(mock.deleteFile(any(), any())).thenAnswer { }
        Mockito.`when`(mock.ingest(any(), any(), anyOrNull(), any())).thenReturn(TestDataFactory.testIngestFileResponse())
        return mock
    }

    @Bean
    @Primary
    fun parsingServiceConnector(): ParsingServiceConnector {
        val mock = Mockito.mock(ParsingServiceConnector::class.java)
        Mockito.`when`(mock.startParsing(any(), any())).thenReturn(TestDataFactory.testParsingTask(false))
        Mockito.`when`(mock.waitTaskFinish(any(), any())).thenReturn(TestDataFactory.testParsingTask(true))
        Mockito.`when`(mock.getParsingTask(any())).thenReturn(TestDataFactory.testParsingTask(true))
        return mock
    }

    @Bean
    @Primary
    fun evaluationServiceConnector(): EvaluationServiceConnector {
        val mock = Mockito.mock(EvaluationServiceConnector::class.java)
        Mockito.`when`(mock.evaluateAnswer(any(), any(), any(), any(), any())).thenReturn(TestDataFactory.testEvaluationResponse())
        return mock
    }

    @Bean
    @Primary
    fun testSetProcessingJob(): TestSetProcessingJob {
        return Mockito.mock(TestSetProcessingJob::class.java)
    }

    @Bean
    @Primary
    fun testSetScheduleProcessingJob(): TestSetScheduleProcessingJob {
        return Mockito.mock(TestSetScheduleProcessingJob::class.java)
    }

    @Bean
    @Primary
    fun expiredLinksCleanupJob(): ExpiredLinksCleanupJob {
        return Mockito.mock(ExpiredLinksCleanupJob::class.java)
    }

    @Bean
    @Primary
    fun rollbackOutdatedOperationsJob(): RollbackOutdatedOperationsJob {
        return Mockito.mock(RollbackOutdatedOperationsJob::class.java)
    }

    @Bean
    @Primary
    fun timedOutEntitiesReleaseJob(): TimedOutEntitiesReleaseJob {
        return Mockito.mock(TimedOutEntitiesReleaseJob::class.java)
    }

    @Bean
    @Primary
    fun processStorageBillingJob(): ProcessStorageBillingJob {
        return Mockito.mock(ProcessStorageBillingJob::class.java)
    }

    @Bean
    @Primary
    fun jaicpServiceConnector(): JaicpServiceConnector {
        val mock = Mockito.mock(JaicpServiceConnector::class.java)
        setupJaicpServiceMock(mock)
        return mock
    }

    @ConditionalOnProperty(prefix = "test-license-key", name = ["enabled"], havingValue = "true")
    @Primary
    @Bean
    fun testLicenseKey(): KeyV1 {
        return TestDataFactory.createTestLicenseKey()
    }

    companion object {
        const val JAICP_PROJECT_ID = 123L

        fun setupJaicpServiceMock(jaicpServiceConnector: JaicpServiceConnector) {
            Mockito.`when`(jaicpServiceConnector.createProjectFromKhubTemplate(any(), any(), any())).thenAnswer { invocation ->
                val projName = invocation.arguments[0] as String
                TestDataFactory.testKhubProject(projName)
            }
            Mockito.`when`(jaicpServiceConnector.getKhubProjects(any())).thenReturn(TestDataFactory.testKhubProjects())
            Mockito.`when`(jaicpServiceConnector.deleteProject(any(), any())).thenAnswer { invocation ->
                val id = invocation.arguments[1] as Long
                BotProjectReadDto(id = id, name = "i`m deleted")
            }
        }
    }
}
