package com.justai.khub.common.mapper

import com.justai.e2echeck.E2EComponentDTO
import com.justai.khub.api.fe.model.AuditDTO
import com.justai.khub.api.fe.model.E2EComponent
import com.justai.khub.api.public.model.BriefSourceInfo
import com.justai.khub.api.public.model.ChunkWithScore
import com.justai.khub.api.public.model.RetrievedChunks
import com.justai.khub.api.public.model.SourceType
import com.justai.khub.common.dto.DocumentChunkWithSource
import com.justai.khub.common.dto.RelevantSource
import com.justai.khub.common.dto.ServiceVersion
import com.justai.khub.common.dto.ServiceVersionResponse
import com.justai.khub.common.entity.AuditableEntity
import com.justai.khub.project.enumeration.SourceType.*
import java.time.ZoneId

object CommonMapper {

    fun AuditableEntity.toAuditDTO(): AuditDTO {
        return AuditDTO(
            createdAt = this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            createdBy = this.createdBy.accountId,
            updatedAt = this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
            updatedBy = this.updatedBy.accountId
        )
    }

    fun List<DocumentChunkWithSource>.toRetrievedChunks(): RetrievedChunks {
        return RetrievedChunks(
            chunks = this.map {
                ChunkWithScore(
                    score = it.chunk.score,
                    content = it.chunk.content,
                    docId = it.source.path,
                    source = it.source.toPublicDTO()
                )
            }
        )
    }

    fun RelevantSource.toPublicDTO() = BriefSourceInfo(
        id = this.sourceId,
        path = this.path,
        title = this.title,
        sourceType = this.sourceType?.toPublicDTO(),
        externalLink = this.externalLink,
    )

    fun com.justai.khub.project.enumeration.SourceType.toPublicDTO() = when (this) {
        CONFLUENCE -> SourceType.CONFLUENCE
        FILE -> SourceType.FILE
        CLOUD_CONFLUENCE -> SourceType.CLOUD_CONFLUENCE
        MINERVA -> SourceType.MINERVA
    }

    fun List<E2EComponentDTO>.toResponse(): List<E2EComponent> {
        return map { it.toResponse() }
    }

    fun E2EComponentDTO.toResponse(): E2EComponent {
        return E2EComponent(
            name = this.name,
            status = this.status,
            startTime = this.startTime,
            elapsedTime = this.elapsedTime,
        )
    }

    fun List<E2EComponent>.toDto(): List<E2EComponentDTO> {
        return map { it.toDto() }
    }

    fun E2EComponent.toDto(): E2EComponentDTO {
        return E2EComponentDTO(
            this.name,
            this.status,
            this.startTime,
            this.elapsedTime ?: -1,
        )
    }

    fun ServiceVersionResponse.toDTO(name: String): ServiceVersion {
        return ServiceVersion(
            name = name,
            version = this.shortVersion ?: this.projectVersion ?: ServiceVersion.UNKNOWN_VERSION,
            status = ServiceVersion.STATUS_OK,
            buildBranch = this.buildBranch,
            buildNumber = this.buildNumber,
            buildDate = this.buildDate,
            buildChangeSet = this.buildChangeSet,
            projectArtifactId = this.projectArtifactId,
            projectVersion = this.projectVersion,
        )
    }
}
