package com.justai.khub.common.dto

data class DocumentChunks(
    val chunks: List<DocumentChunk>
)

data class DocumentChunk(
    val id: String,
    val docId: String,
    val score: Double,
    val content: String,
    val summary: String? = null,
    val questions: List<String>? = null,
    val keywords: List<String>? = null,
)

data class DocumentChunkWithSource(
    val chunk: DocumentChunk,
    val source: RelevantSource,
)
