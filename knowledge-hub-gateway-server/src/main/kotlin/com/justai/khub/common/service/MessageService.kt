package com.justai.khub.common.service

import org.springframework.context.MessageSource
import org.springframework.context.i18n.LocaleContextHolder
import org.springframework.stereotype.Service
import java.util.*

@Service
class MessageService(
    private val messageSource: MessageSource
) {

    fun getMessage(code: String): String {
        return messageSource.getMessage(code, null, code, Locale.getDefault()) ?: code
    }

    fun getMessageByPrefix(prefix: String): String {
        val locale = LocaleContextHolder.getLocale()
        val code = "$prefix.${locale.language}"
        return messageSource.getMessage(code, null, code, locale) ?: code
    }
}
