package com.justai.khub.common.configuration

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "local-features", ignoreUnknownFields = true)
data class LocalFeaturesConfiguration(
    val chunksRetrieving: <PERSON><PERSON>an,
    val channels: <PERSON><PERSON><PERSON>,
    val customQAPromptsEnabled: <PERSON><PERSON><PERSON>,
    val qaModuleEnabled: <PERSON><PERSON><PERSON>,
    val tariffInfoEnabled: <PERSON><PERSON>an,
    val minervaEnabled: Boolean,
) {
    fun serialize(): List<String> {
        return buildList {
            if (chunksRetrieving) add("chunksRetrieving")
            if (channels) add("channels")
            if (customQAPromptsEnabled) add("customQAPromptsEnabled")
            if (qaModuleEnabled) add("qaModuleEnabled")
            if (tariffInfoEnabled) add("tariffInfoEnabled")
            if (minervaEnabled) add("minervaEnabled")
        }
    }

    companion object {
        const val BILLING_ENABLED_FEATURE = "archivedBillingEnabled"
    }
}
