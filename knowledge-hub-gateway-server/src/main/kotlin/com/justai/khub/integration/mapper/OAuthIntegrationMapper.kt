package com.justai.khub.integration.mapper

import com.justai.khub.common.service.MessageService
import org.springframework.stereotype.Component

@Component
class OAuthIntegrationMapper(
    private val messageService: MessageService,
) {
    fun generateOAuthResultHTML(success: Boolean): String {
        val key = if (success) "success" else "failure"
        val title = messageService.getMessage("project.settings.oauth.$key.title")
        val result = messageService.getMessageByPrefix("project.settings.oauth.$key.body")
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>$title</title>
                <style>
                    html, body {
                        height: 100%;
                        margin: 0;
                        padding: 0;
                        font-family: Roboto, sans-serif;
                        background-color: rgb(228, 229, 230);
                    }
                    .content {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100%;
                        text-align: center;
                        font-size: 17px;
                    }
                </style>
            </head>
            <body>
                <div class="content">
                    $result
                </div>
            </body>
            </html>
        """.trimIndent()
    }
}
