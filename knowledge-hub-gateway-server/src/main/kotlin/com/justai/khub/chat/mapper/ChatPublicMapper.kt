package com.justai.khub.chat.mapper


import com.justai.khub.api.public.model.ChatQueryProcessingResult
import com.justai.khub.api.public.model.QueryProcessingStatus
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.mapper.CommonMapper.toPublicDTO
import java.time.ZoneId

object ChatPublicMapper {

    fun ChatHistoryRecordEntity.toChatQuery(): ChatQueryProcessingResult {
        return ChatQueryProcessingResult(
            id = this.id,
            chatId = this.chat.id,
            request = this.request,
            status = this.status.toQueryProcessingStatus(),
            createdAt = this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            response = if (this.status == ChatHistoryRecordStatus.FINISHED) this.response else null,
            updatedAt = this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
            comment = if (this.status != ChatHistoryRecordStatus.FINISHED) this.response else null,
            relevantSources = if (this.status == ChatHistoryRecordStatus.FINISHED) {
                this.addInfo?.relevantSources?.map { it.toPublicDTO() }
            } else null,
        )
    }

    fun ChatHistoryRecordStatus.toQueryProcessingStatus(): QueryProcessingStatus {
        return QueryProcessingStatus.valueOf(this.name)
    }
}
