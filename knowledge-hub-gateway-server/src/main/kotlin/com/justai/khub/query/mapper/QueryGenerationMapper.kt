package com.justai.khub.query.mapper

import com.justai.khub.api.public.model.HistoryRecord
import com.justai.khub.api.public.model.QueryProcessingResult
import com.justai.khub.api.public.model.QueryProcessingStatus
import com.justai.khub.chat.entity.ChatHistoryRecordEntity
import com.justai.khub.chat.enumeration.ChatHistoryRecordStatus
import com.justai.khub.common.mapper.CommonMapper.toPublicDTO
import com.justai.khub.query.dto.ParticipantRole
import com.justai.khub.query.dto.UserHistory
import com.justai.khub.query.dto.UserHistoryRecord
import java.time.ZoneId

object QueryGenerationMapper {

    fun ChatHistoryRecordEntity.toQueryProcessingResult(): QueryProcessingResult {
        return QueryProcessingResult(
            id = this.id,
            request = this.request,
            status = this.status.toQueryProcessingStatus(),
            createdAt = this.createdAt.atZone(ZoneId.systemDefault()).toInstant(),
            response = if (this.status == ChatHistoryRecordStatus.FINISHED) this.response else null,
            updatedAt = this.updatedAt.atZone(ZoneId.systemDefault()).toInstant(),
            comment = if (this.status != ChatHistoryRecordStatus.FINISHED) this.response else null,
            relevantSources = this.addInfo?.relevantSources?.map { it.toPublicDTO() }
        )
    }

    fun ChatHistoryRecordStatus.toQueryProcessingStatus(): QueryProcessingStatus {
        return QueryProcessingStatus.valueOf(this.name)
    }

    fun List<HistoryRecord>.toUserHistory(): UserHistory {
        return UserHistory(this.map { record ->
            UserHistoryRecord(message = record.content, role = ParticipantRole.valueOf(record.role.value))
        })
    }
}
