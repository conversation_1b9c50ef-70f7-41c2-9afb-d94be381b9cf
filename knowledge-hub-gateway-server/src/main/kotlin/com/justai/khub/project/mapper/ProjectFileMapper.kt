package com.justai.khub.project.mapper

import com.justai.khub.api.fe.model.SourceDTO
import com.justai.khub.api.fe.model.SourceType
import com.justai.khub.common.dto.RelevantSource
import com.justai.khub.common.mapper.CommonMapper.toAuditDTO
import com.justai.khub.integration.dto.*
import com.justai.khub.integration.entity.IntegrationEntity
import com.justai.khub.project.dto.ProjectFileMetadata
import com.justai.khub.project.entity.ProjectFileEntity
import com.justai.khub.project.entity.ProjectVersionEntity
import com.justai.khub.project.enumeration.ProjectFileType
import com.justai.khub.project.enumeration.ProjectSourceStatus
import org.apache.commons.io.FilenameUtils
import org.slf4j.LoggerFactory
import java.nio.charset.StandardCharsets

object ProjectFileMapper {
    const val DEFAULT_SORT = "relativePath"
    private val NON_DOCUMENT_ATTACHMENTS = setOf("svg", "jpg", "jpeg", "png", "gif")
    private val log = LoggerFactory.getLogger(this::class.java)

    private fun makeConfluenceUrl(host: String, pageId: String): String =
        "${host.removeSuffix("/")}/pages/viewpage.action?pageId=$pageId"

    private fun makeCloudConfluenceUrl(host: String?, spaceKey: String?, pageId: String): String =
        "$host/wiki/spaces/$spaceKey/pages/$pageId"

    private fun makeMinervaArticleUrl(host: String, spaceId: String, articleId: String): String =
        "${host.removeSuffix("/")}/content/space/$spaceId/article/$articleId"

    private fun makeMinervaAttachmentUrl(host: String, spaceId: String, fileId: String): String =
        "${host.removeSuffix("/")}/api/storage/space/$spaceId/file/$fileId"


    fun ProjectFileEntity.toDTO(type: SourceType = SourceType.FILE): SourceDTO {
        return SourceDTO(
            id = this.id,
            name = this.relativePath,
            segment = this.metadata.segment,
            type = type,
            version = this.version.name,
            audit = this.toAuditDTO(),
            sizeBytes = this.sizeBytes,
            sizeChars = this.sizeChars,
            status = this.status.toString(),
            lastError = this.lastError
        )
    }

    fun initEntity(
        version: ProjectVersionEntity,
        metadata: ProjectFileMetadata,
        path: String,
        sizeBytes: Int,
        autoIngest: Boolean,
    ): ProjectFileEntity {
        return ProjectFileEntity().also {
            it.version = version
            it.status = if (autoIngest) ProjectSourceStatus.READY_TO_INGEST else ProjectSourceStatus.NOT_INGESTED
            it.fileType = ProjectFileType.DOCUMENT
            it.relativePath = path
            it.pathInStorage = path
            it.sizeBytes = sizeBytes
            it.metadata = metadata
        }
    }

    fun mapFileSort(clientSort: String?): String {
        return when (clientSort) {
            "relativePath" -> clientSort
            "createdAt" -> clientSort
            "status" -> clientSort
            "sizeBytes" -> clientSort
            null -> DEFAULT_SORT
            else -> {
                log.warn("Unexpected project files sort: {}", clientSort)
                DEFAULT_SORT
            }
        }
    }

    fun ProjectFileEntity.resetContentRelatedProperties(newSizeBytes: Int) {
        this.sizeBytes = newSizeBytes
        this.sizeChars = null
        this.parsedFileName = null
        this.usedPromptTokens = null
        this.usedCompletionTokens = null
    }

    fun IntegrationSettings.toSourceType(): com.justai.khub.project.enumeration.SourceType = when (this) {
        is MinervaIntegrationSettings -> com.justai.khub.project.enumeration.SourceType.MINERVA
        is ConfluenceIntegrationSettings -> com.justai.khub.project.enumeration.SourceType.CONFLUENCE
        is CloudConfluenceIntegrationSettings -> com.justai.khub.project.enumeration.SourceType.CLOUD_CONFLUENCE
    }

    // WARN! 'existedEntity' should be updated param
    fun PageResponse.createOrUpdateEntity(existedEntity: ProjectFileEntity?, integration: IntegrationEntity): ProjectFileEntity {
        val pagePath = path()
        return this.toEntity(existedEntity, pagePath, integration).also {
            it.resetContentRelatedProperties(this.body.exportView.value.toByteArray(StandardCharsets.UTF_8).size)
        }
    }

    fun AttachmentWithLink.createOrUpdateEntity(
        existedEntity: ProjectFileEntity?,
        integration: IntegrationEntity,
        parentEntity: ProjectFileEntity,
    ): ProjectFileEntity {
        val pagePath = this.attachment.path(parentEntity)
        return this.attachment.toEntity(existedEntity, pagePath, integration).also {
            it.parent = parentEntity
            it.metadata = it.metadata.copy(
                segment = parentEntity.metadata.segment,
                externalLink = when (val settings = integration.settings) {
                    is ConfluenceSettingsCommon -> this.downloadLink.link
                    is MinervaIntegrationSettings -> makeMinervaAttachmentUrl(
                        host = settings.baseUrl,
                        spaceId = settings.spaceId,
                        fileId = this.attachment.id,
                    )
                },
                title = this.attachment.title ?: existedEntity?.parsedFileName ?: existedEntity?.relativePath?.substringAfterLast("/"),
                type = integration.settings.toSourceType(),
                extension = FilenameUtils.getExtension(pagePath).lowercase()
            )
        }
    }

    fun PageResponse.path(): String {
        val ancestorTitles = this.ancestors.joinToString(separator = "/") { it.title ?: it.id }
        val pagePath = if (ancestorTitles.isEmpty()) "${this.title}.html" else "$ancestorTitles/${this.title}.html"
        return pagePath
    }

    fun AttachmentResponse.path(parentEntity: ProjectFileEntity): String {
        val ancestorTitles = parentEntity.metadata.path ?: parentEntity.relativePath
        val pagePath = if (ancestorTitles.isEmpty()) this.title ?: this.id else "$ancestorTitles/${this.title}"
        return pagePath
    }

    private fun ContentResponse.toEntity(existedEntity: ProjectFileEntity?, pathToSource: String, integration: IntegrationEntity) : ProjectFileEntity {
        return (existedEntity ?: ProjectFileEntity()).also {
            it.version = integration.version
            it.integrationId = integration.id
            it.metadata = ProjectFileMetadata(
                path = pathToSource,
                externalLink = toExternalLink(integration),
                title = this.title ?: existedEntity?.parsedFileName ?: existedEntity?.relativePath?.substringAfterLast("/"),
                type = integration.settings.toSourceType()
            )
            it.status = if (integration.autoIngest) ProjectSourceStatus.READY_TO_INGEST else ProjectSourceStatus.NOT_INGESTED
            it.fileType = pathToSource.toProjectFileType()
            it.relativePath = "${integration.name}/$pathToSource"
            it.pathInStorage = "${integration.id}/$pathToSource"
            it.contentVersion = "${this.version.number}"
            it.externalId = this.id
            it.contentVersion = "${this.version.number}"
        }
    }

    private fun String.toProjectFileType(): ProjectFileType {
        val extension = FilenameUtils.getExtension(this).lowercase()
        return if (NON_DOCUMENT_ATTACHMENTS.contains(extension)) {
            ProjectFileType.ATTACHMENT
        } else {
            ProjectFileType.DOCUMENT
        }
    }

    fun ContentResponse.toExternalLink(integration: IntegrationEntity): String? {
        return when (val settings = integration.settings) {
            is ConfluenceIntegrationSettings -> makeConfluenceUrl(
                host = settings.baseUrl,
                pageId = this.id,
            )
            is CloudConfluenceIntegrationSettings -> makeCloudConfluenceUrl(
                host = settings.siteUrl,
                spaceKey = settings.spaceKey,
                pageId = this.id,
            )
            is MinervaIntegrationSettings -> makeMinervaArticleUrl(
                host = settings.baseUrl,
                spaceId = settings.spaceId,
                articleId = this.id,
            )
        }
    }

    fun ProjectFileEntity.toRelevantSource(defaultExternalLink: String? = null): RelevantSource {
        return RelevantSource(
            sourceId = this.id,
            path = this.relativePath,
            title = this.metadata.title ?: this.parsedFileName ?: this.relativePath.substringAfterLast("/"),
            sourceType = com.justai.khub.project.enumeration.SourceType.valueOf(
                this.metadata.type?.name ?: SourceType.FILE.name
            ),
            externalLink = this.metadata.externalLink ?: defaultExternalLink,
        )
    }
}
