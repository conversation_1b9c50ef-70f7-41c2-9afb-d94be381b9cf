project.settings.indexation.title.ru=Индексация
project.settings.indexation.title.en=Indexing
project.settings.indexation.menutitle.ru=Индексация
project.settings.indexation.menutitle.en=Indexing
project.settings.indexation.description.ru=Индексация базы знаний включает подготовку загруженных данных, разделение текста на небольшие фрагменты (чанки) и векторизацию
project.settings.indexation.description.en=Indexing the knowledge base includes preparing the uploaded data, chunking the text into smaller fragments, and vectorisation
project.settings.indexation.docs.label.ru=Документация по разделу
project.settings.indexation.docs.label.en=Section documentation
project.settings.indexation.docs.link.ru=/docs/guide/training-settings
project.settings.indexation.docs.link.en=/docs/guide/training-settings
project.settings.indexation.vectorizer.title.ru=Модель векторизатора
project.settings.indexation.vectorizer.title.en=Vectoriser model
project.settings.indexation.vectorizer.description.ru=Векторизация — это преобразование текста в числовые векторы, с которыми будут работать алгоритмы. Применяется как к вашим данным на этапе индексации, так и к запросам пользователей.
project.settings.indexation.vectorizer.description.en=Vectorisation is the conversion of text into numerical vectors, which can be processed by algorithms. It applies to both your data at the indexing stage and to users’ queries.
project.settings.indexation.vectorizer.type.options.text_embedding_3_large.title.ru=text-embedding-3-large
project.settings.indexation.vectorizer.type.options.text_embedding_3_large.title.en=text-embedding-3-large
project.settings.indexation.vectorizer.type.options.text_embedding_3_large.description.ru=Модель компании OpenAI
project.settings.indexation.vectorizer.type.options.text_embedding_3_large.description.en=OpenAI’s model
project.settings.indexation.vectorizer.type.options.multilingual_e5_large.title.ru=intfloat/multilingual-e5-large
project.settings.indexation.vectorizer.type.options.multilingual_e5_large.title.en=intfloat/multilingual-e5-large
project.settings.indexation.vectorizer.type.options.multilingual_e5_large.description.ru=Модель в контуре {company}
project.settings.indexation.vectorizer.type.options.multilingual_e5_large.description.en=Model within {company}’s infrastructure

project.settings.indexation.chunker.chunk_size.title.ru=Размер чанка
project.settings.indexation.chunker.chunk_size.title.en=Chunk size
project.settings.indexation.chunker.chunk_size.label.ru=Максимальный размер чанка, в символах
project.settings.indexation.chunker.chunk_size.label.en=Max chunk size in characters
project.settings.indexation.chunker.type.label.ru=Способ разделения текста
project.settings.indexation.chunker.type.label.en=Chunking method
project.settings.indexation.chunker.type.options.sentence.title.ru=По длине
project.settings.indexation.chunker.type.options.sentence.title.en=By length
project.settings.indexation.chunker.type.options.sentence.description.ru=
project.settings.indexation.chunker.type.options.sentence.description.en=
project.settings.indexation.chunker.type.options.llm.title.ru=С помощью LLM
project.settings.indexation.chunker.type.options.llm.title.en=Using LLM
project.settings.indexation.chunker.type.options.llm.description.ru=
project.settings.indexation.chunker.type.options.llm.description.en=
project.settings.indexation.chunker.language.label.ru=Язык
project.settings.indexation.chunker.language.label.en=Language
project.settings.indexation.chunker.language.options.english.description.ru=
project.settings.indexation.chunker.language.options.english.description.en=
project.settings.indexation.chunker.language.options.english.title.ru=Английский
project.settings.indexation.chunker.language.options.english.title.en=English
project.settings.indexation.chunker.language.options.russian.description.ru=
project.settings.indexation.chunker.language.options.russian.description.en=
project.settings.indexation.chunker.language.options.russian.title.ru=Русский
project.settings.indexation.chunker.language.options.russian.title.en=Russian
project.settings.indexation.chunker.tokens_soft_limit.label.ru=Средний размер чанка, в токенах
project.settings.indexation.chunker.tokens_soft_limit.label.en=Average chunk size in tokens
project.settings.indexation.chunker.tokens_soft_limit.hint.ru=Если текстовая единица меньше указанного значения, то она не будет разбиваться на более мелкие смысловые части, например документ на главы или главы на подглавы
project.settings.indexation.chunker.tokens_soft_limit.hint.en=If a text unit is smaller than the specified value, it will not be chunked into smaller semantic parts, such as a document into chapters or chapters into sections
project.settings.indexation.chunker.use_for_large_tables.label.ru=Специальный чанкинг для больших таблиц
project.settings.indexation.chunker.use_for_large_tables.label.en=Special chunking for large tables
project.settings.indexation.chunker.use_for_large_tables.hint.ru=Большие таблицы, которые модель не может обработать самостоятельно, будут разбиты на части. В каждом чанке будет включена строка с заголовками столбцов. Это позволяет модели лучше понять структуру данных и сгенерировать более точный ответ.
project.settings.indexation.chunker.use_for_large_tables.hint.en=Large tables that the model cannot process at once will be chunked into smaller parts, each containing the column headers. This helps the model better understand the data structure and generate more accurate answers.
project.settings.indexation.vectorizer.model.label.ru=Модель
project.settings.indexation.vectorizer.model.label.en=Model

project.settings.search.root.type.menutitle.ru=Поиск
project.settings.search.root.type.menutitle.en=Retrieval
project.settings.search.root.type.title.ru=Поиск чанков
project.settings.search.root.type.title.en=Chunk retrieval
project.settings.search.root.type.onChange.hint.ru=Пайплайн был изменен. Убедитесь в корректности настроек в разделе «Генерация».
project.settings.search.root.type.onChange.hint.en=Pipeline has been changed. Make sure the settings in the “Generation” section are correct.
project.settings.search.root.type.options.semantic.title.ru=Семантический пайплайн
project.settings.search.root.type.options.semantic.title.en=Semantic pipeline
project.settings.search.root.type.options.semantic.description.ru=База знаний подбирает чанки, подходящие по смыслу, сравнивания векторные представления чанков и запроса
project.settings.search.root.type.options.semantic.description.en=The knowledge base selects relevant chunks by comparing the vector representations of chunks and the query
project.settings.search.root.type.options.agent.title.ru=Агентский пайплайн
project.settings.search.root.type.options.agent.title.en=Agentic pipeline
project.settings.search.root.type.options.agent.description.ru=AI-агент запрашивает чанки, используя вызовы функций
project.settings.search.root.type.options.agent.description.en=The AI agent retrieves chunks for the response using function calls
project.settings.search.root.type.hint.ru=Поиск в базе знаний информации, которая соответствует запросу пользователя и будет использоваться для генерации ответа
project.settings.search.root.type.hint.en=Search the knowledge base for data that matches the user’s query and will be used to generate a response
project.settings.search.docs.label.ru=Документация по разделу
project.settings.search.docs.label.en=Section documentation
project.settings.search.docs.link.ru=/docs/guide/retrieval-settings
project.settings.search.docs.link.en=/docs/guide/retrieval-settings


project.settings.llm.contextWindow.hint.ru=Максимальное количество токенов, которое может быть отправлено в LLM
project.settings.llm.contextWindow.hint.en=Maximum number of tokens that can be sent to the LLM
project.settings.llm.contextWindow.label.ru=Максимальное количество токенов в запросе
project.settings.llm.contextWindow.label.en=Max tokens in request
project.settings.llm.frequencyPenalty.hint.ru=Контролирует частоту повторений слов и фраз в ответах. Более высокие значения помогут избавиться от повторов
project.settings.llm.frequencyPenalty.hint.en=Controls the repetition frequency of words and phrases in answers. Higher values help reduce repetitions
project.settings.llm.frequencyPenalty.label.ru=Frequency penalty
project.settings.llm.frequencyPenalty.label.en=Frequency penalty
project.settings.llm.maxTokens.hint.ru=Максимальное количество токенов, которое может быть сгенерировано LLM за одну итерацию
project.settings.llm.maxTokens.hint.en=Maximum number of tokens that can be generated by the LLM in one iteration
project.settings.llm.maxTokens.label.ru=Максимальное количество токенов в ответе
project.settings.llm.maxTokens.label.en=Max tokens in response
project.settings.llm.title.ru=Настройки LLM
project.settings.llm.title.en=LLM settings
project.settings.llm.titleextended.ru=Расширенные настройки LLM
project.settings.llm.titleextended.en=Advanced LLM settings
project.settings.llm.model.label.ru=Модель
project.settings.llm.model.label.en=Model
project.settings.llm.presencePenalty.hint.ru=Влияет на многообразие слов и фраз в ответах. Более высокие значения помогут избавиться от повторов
project.settings.llm.presencePenalty.hint.en=Affects the variety of words and phrases in answers. Higher values help reduce repetitions
project.settings.llm.presencePenalty.label.ru=Presence penalty
project.settings.llm.presencePenalty.label.en=Presence penalty
project.settings.llm.temperature.hint.ru=Регулирует креативность ответов. При более высоких значениях результаты будут более творческими и менее предсказуемыми
project.settings.llm.temperature.hint.en=Controls the creativity of answers. At higher values, the results are more creative and less predictable
project.settings.llm.temperature.label.ru=Температура
project.settings.llm.temperature.label.en=Temperature
project.settings.llm.topP.hint.ru=Регулирует разнообразность ответов. При низких значениях нейросеть выбирает из меньшего количества вероятных слов, но при высоких — ответ может получиться разнообразнее. Рекомендуем менять или Top P, или температуру, но не оба параметра одновременно
project.settings.llm.topP.hint.en=Controls the diversity of answers. At lower values, the model selects from a smaller number of probable words, but at higher values, the answer may be more varied. We recommend changing either the Top P or the temperature, but not both parameters at once
project.settings.llm.topP.label.ru=Top P
project.settings.llm.topP.label.en=Top P

project.settings.search.sources.label.ru=Показывать документы-источники в ответе бота
project.settings.search.sources.label.en=Show source documents in bot response
project.settings.search.sources.hint.ru=В каждом ответе показывать, на основе каких файлов или страниц он сформирован
project.settings.search.sources.hint.en=In each answer, show which files or pages it is based on

project.settings.search.reranker.title.ru=Повторное ранжирование
project.settings.search.reranker.title.en=Re-ranking
project.settings.search.reranker.hint.ru=Повторная оценка релевантности чанков запросу с целью предоставить наиболее подходящую информацию. Если результаты поиска в источниках достаточно хорошо соответствуют запросу, повторное ранжирование можно отключить
project.settings.search.reranker.hint.en=Re-assessment of the chunk relevance to the query in order to provide the most appropriate information. If the retrieval results match the query well enough, you can disable re-ranking
project.settings.search.reranker.type.options.manual.description.ru=Собственный алгоритм {company}
project.settings.search.reranker.type.options.manual.description.en={company} proprietary algorithm
project.settings.search.reranker.type.options.manual.title.ru=Эмпирическое
project.settings.search.reranker.type.options.manual.title.en=Empirical
project.settings.search.reranker.type.options.model.title.ru=С помощью модели
project.settings.search.reranker.type.options.model.title.en=Using model
project.settings.search.reranker.type.options.model.description.ru=Более точное, чем эмпирическое, но менее быстрое
project.settings.search.reranker.type.options.model.description.en=More accurate than empirical, but slower
project.settings.search.reranker.maxChunks.hint.ru=Максимальное количество результатов, которые после повторного ранжирования будут отправлены в модель для генерации ответа
project.settings.search.reranker.maxChunks.hint.en=Maximum number of results that will be sent to the model after re-ranking to generate a response
project.settings.search.reranker.maxChunks.label.ru=Количество результатов поиска
project.settings.search.reranker.maxChunks.label.en=Number of retrieval results
project.settings.search.reranker.maxChunksPerDoc.hint.ru=Максимальное количество используемых результатов поиска из одного источника (страницы, файла). Установите это ограничение, если предпочтительно формировать ответ на основе нескольких источников
project.settings.search.reranker.maxChunksPerDoc.hint.en=Maximum number of retrieval results from a single source (page, file). Set the limit if it is preferable to generate a response based on multiple sources
project.settings.search.reranker.maxChunksPerDoc.label.ru=Количество результатов из одного документа
project.settings.search.reranker.maxChunksPerDoc.label.en=Number of results from a single document
project.settings.search.reranker.minScore.hint.ru=Чанки с оценкой релевантности ниже порога не будут оправлены в модель для генерации ответа
project.settings.search.reranker.minScore.hint.en=Chunks whose relevance score is below the threshold will not be sent to the model to generate a response
project.settings.search.reranker.minScore.label.ru=Пороговая оценка
project.settings.search.reranker.minScore.label.en=Threshold score
project.settings.search.reranker.scoreReductionLimit.hint.ru=Чанки, оценка которых слишком отличается от самого релевантного чанка, не будут оправлены в модель для генерации ответа
project.settings.search.reranker.scoreReductionLimit.hint.en=Chunks whose score differs significantly from the most relevant chunk will not be sent to the model to generate a response
project.settings.search.reranker.scoreReductionLimit.label.ru=Максимальное отличие от лучшей оценки, в %
project.settings.search.reranker.scoreReductionLimit.label.en=Maximum deviation from the best score, %

project.settings.search.abbreviations.title.ru=Аббревиатуры
project.settings.search.abbreviations.title.en=Abbreviations
project.settings.search.abbreviations.hint.ru=В некоторых документах могут быть специфические сокращения. Помогите расшифровать их
project.settings.search.abbreviations.hint.en=Some documents may contain specific abbreviations. Help decipher them
project.settings.search.abbreviations.template.label.ru=Скачать шаблон JSON
project.settings.search.abbreviations.template.label.en=Download JSON template

project.settings.advanced.title.ru=Расширенные настройки
project.settings.advanced.title.en=Advanced settings
project.settings.search.advanced.csv_processing_enabled.label.ru=Обработка файлов CSV
project.settings.search.advanced.csv_processing_enabled.label.en=Process CSV files

project.settings.search.response_generator.csv_processor_prompt_adjustment.label.ru=Уточнение к промту для CSV
project.settings.search.response_generator.csv_processor_prompt_adjustment.label.en=Prompt appendage for CSV
project.settings.search.response_generator.csv_processor_prompt_adjustment.hint.ru=Добавляется в конец системного промта, используемого для обработки файлов CSV
project.settings.search.response_generator.csv_processor_prompt_adjustment.hint.en=This is added to the end of the system prompt used for CSV file processing

project.settings.search.response_generator.title.ru=Генерация ответа
project.settings.search.response_generator.title.en=Response generation
project.settings.search.response_generator.hint.ru=Генерация ответа на запрос пользователя на основе результатов поиска. Выполняется с помощью LLM
project.settings.search.response_generator.hint.en=Generating a response to the user’s query based on the retrieval results. Performed using LLM
project.settings.search.response_generator.menutitle.ru=Генерация
project.settings.search.response_generator.menutitle.en=Generation
project.settings.search.response_generator.prompt.label.ru=Системный промт
project.settings.search.response_generator.prompt.label.en=System prompt
project.settings.search.response_generator.docs.link.ru=/docs/guide/rag-settings
project.settings.search.response_generator.docs.link.en=/docs/guide/rag-settings
project.settings.search.response_generator.systemPrompt.label.ru=Системный промт
project.settings.search.response_generator.systemPrompt.label.en=System prompt

project.settings.search.retrieving.title.ru=Параметры поиска чанков в источниках
project.settings.search.retrieving.title.en=Parameters of chunk retrieval from sources
project.settings.search.retrieving.description.ru=Какие чанки будут выбраны для дальнейшей обработки — переранжирования или генерации ответа
project.settings.search.retrieving.description.en=Which chunks will be selected for further processing – re-ranking or response generation

project.settings.search.retrieving.similarityTopK.label.ru=Количество результатов поиска
project.settings.search.retrieving.similarityTopK.label.en=Number of retrieval results

project.settings.search.retrieving.similarityTopK.hint.ru=Количество найденных чанков, которые будут переданы для дальнейшей обработки
project.settings.search.retrieving.similarityTopK.hint.en=The number of found chunks that will be passed for further processing
project.settings.search.retrieving.candidateRadius.label.ru=Количество соседних чанков
project.settings.search.retrieving.candidateRadius.label.en=Number of adjacent chunks
project.settings.search.retrieving.candidateRadius.hint.ru=Количество соседних чанков каждого найденного чанка, которые будут переданы вместе с ним
project.settings.search.retrieving.candidateRadius.hint.en=The number of adjacent chunks of each found chunk that will be passed along with it

project.settings.search.retrieving.useMetadata.label.ru=Поиск по метаданным чанков
project.settings.search.retrieving.useMetadata.label.en=Search by chunk metadata
project.settings.search.retrieving.useMetadata.hint.ru=Поиск по дополнительным полям чанков, которые были заполнены при индексации
project.settings.search.retrieving.useMetadata.hint.en=Search by additional chunk fields that were filled during indexing

project.settings.search.retrieving.fullTextSearch.label.ru=Полнотекстовый поиск
project.settings.search.retrieving.fullTextSearch.label.en=Full-text search
project.settings.search.retrieving.fullTextSearch.hint.ru=Поиск по словам из запроса. Полнотекстовый поиск можно использовать в дополнение к семантическому поиску, который учитывает смысл и контекст запроса
project.settings.search.retrieving.fullTextSearch.hint.en=Search based on words of the query. Full-text search can complement semantic search, which considers the meaning and context of the query
project.settings.search.retrieving.fullTextSearch.options.hybrid.title.ru=Гибридный
project.settings.search.retrieving.fullTextSearch.options.hybrid.title.en=Hybrid
project.settings.search.retrieving.fullTextSearch.options.hybrid.description.ru=Объединяет все полученные результаты семантического и полнотекстового поиска
project.settings.search.retrieving.fullTextSearch.options.hybrid.description.en=Combine all received results of semantic and full-text searches
project.settings.search.retrieving.fullTextSearch.options.weighted.title.ru=Взвешенный
project.settings.search.retrieving.fullTextSearch.options.weighted.title.en=Weighted
project.settings.search.retrieving.fullTextSearch.options.weighted.description.ru=Отбирает заданное количество результатов семантического и полнотекстового поиска
project.settings.search.retrieving.fullTextSearch.options.weighted.description.en=Select a specified number of results of semantic and full-text searches
project.settings.search.retrieving.fullTextSearch.options.threshold.title.ru=С порогом
project.settings.search.retrieving.fullTextSearch.options.threshold.title.en=With threshold
project.settings.search.retrieving.fullTextSearch.options.threshold.description.ru=Если семантический поиск не предоставил достаточно релевантные результаты, выдает результаты полнотекстового поиска
project.settings.search.retrieving.fullTextSearch.options.threshold.description.en=If semantic search hasn’t provided sufficiently relevant results, return the full-text search results
project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.label.ru=Порция семантики
project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.label.en=Semantic portion
project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.hint.ru=Сколько лучших семантических результатов будет включено в конечный результат. Рекомендуемое значение — 10
project.settings.search.retrieving.fullTextSearch.hybrid.semanticPortion.hint.en=Number of the best semantic results to be included in the final result. The recommended value is 10
project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.label.ru=Порция полнотекстового поиска
project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.label.en=Full-text search portion
project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.hint.ru=Сколько лучших результатов полнотекстового поиска будет включено в конечный результат. Рекомендуемое значение — 1 или 2
project.settings.search.retrieving.fullTextSearch.hybrid.ftsPortion.hint.en=Number of the best full-text search results to be included in the final result. The recommended value is 1 or 2
project.settings.search.retrieving.fullTextSearch.threshold.label.ru=Пороговая оценка
project.settings.search.retrieving.fullTextSearch.threshold.label.en=Threshold score
project.settings.search.retrieving.fullTextSearch.threshold.hint.ru=Если оценка релевантности ниже порога для всех семантических результатов, поиск переключается на полнотекстовый
project.settings.search.retrieving.fullTextSearch.threshold.hint.en=If the relevance score is below the threshold for all semantic results, the search switches to full-text mode


project.settings.search.retrieving.rephraseQuery.label.ru=Перефразировать запрос пользователя
project.settings.search.retrieving.rephraseQuery.label.en=Rephrase user’s query
project.settings.search.retrieving.queryRephrasePrompt.label.ru=Промт
project.settings.search.retrieving.queryRephrasePrompt.label.en=Prompt
project.settings.search.retrieving.queryRephrasePrompt.hint.ru=Допустимый плейсхолдер: {query_str} — запрос
project.settings.search.retrieving.queryRephrasePrompt.hint.en=Acceptable placeholder: {query_str} – query

project.settings.search.retrieving.useHistory.label.ru=Учeт истории диалога
project.settings.search.retrieving.useHistory.label.en=Consider chat history
project.settings.search.retrieving.historyCondensePrompt.label.ru=Промт
project.settings.search.retrieving.historyCondensePrompt.label.en=Prompt
project.settings.search.retrieving.historyCondensePrompt.hint.ru=Допустимые плейсхолдеры: {history_str} — история диалога, {query_str} — запрос
project.settings.search.retrieving.historyCondensePrompt.hint.en=Acceptable placeholders: {history_str} – chat history, {query_str} – query
project.settings.search.retrieving.historyMaxSize.label.ru=Максимальный размер истории диалога, в токенах
project.settings.search.retrieving.historyMaxSize.label.en=Max history size in tokens
project.settings.search.retrieving.historyMinUserMessages.label.ru=Минимальное количество запросов пользователя
project.settings.search.retrieving.historyMinUserMessages.label.en=Min number of user’s queries
project.settings.search.retrieving.historyMinUserMessages.hint.ru=Если размер истории превышает ограничение по токенам, сообщения удаляются по одному, пока не будет достигнут нужный размер или минимальное количество запросов. Если минимальное количество запросов достигнуто раньше, история отправляется в LLM с превышением размера, что может привести к ошибке
project.settings.search.retrieving.historyMinUserMessages.hint.en=If the history size exceeds the token limit, chat messages are removed one by one until the token limit or the minimum number of queries is reached. If the minimum number of queries is reached first, the history is sent to the LLM exceeding the token limit, which may result in an error

project.settings.oauth.success.body.ru=Авторизация прошла успешно, можно закрыть вкладку
project.settings.oauth.success.body.en=Authorisation was successful, you can close the tab
project.settings.oauth.success.title.ru=Авторизация прошла успешно
project.settings.oauth.success.title.en=Authorisation was successful
project.settings.oauth.failure.body.ru=Авторизация не удалась, попробуйте ещё раз. Если проблема не уйдет, обратитесь в поддержку
project.settings.oauth.failure.body.en=Authorisation failed, please try again. If the problem persists, contact support
project.settings.oauth.failure.title.ru=Авторизация не удалась
project.settings.oauth.failure.title.en=Authorisation failed

apiKeys.request.sample.projectInfo.hint.ru=Информация по проекту базы знаний
apiKeys.request.sample.projectInfo.hint.en=Knowledge base project info
apiKeys.request.sample.generateQuery.hint.ru=Запрос на генерацию ответа к базе знаний
apiKeys.request.sample.generateQuery.hint.en=Request to the knowledge base to generate a response

dashboard.billing.filter.defaultValue.en=All
dashboard.billing.filter.defaultValue.ru=Все

dashboard.billing.filter.model.displayName.en=Model
dashboard.billing.filter.model.displayName.ru=Модель
dashboard.billing.filter.operationType.displayName.en=Consumption type
dashboard.billing.filter.operationType.displayName.ru=Тип расхода
dashboard.billing.filter.tokenType.displayName.en=Token type
dashboard.billing.filter.tokenType.displayName.ru=Тип токена

dashboard.billing.filter.operationType.userQuery.en=User requests
dashboard.billing.filter.operationType.userQuery.ru=Пользовательские запросы
dashboard.billing.filter.operationType.agentQuery.en=Agent requests
dashboard.billing.filter.operationType.agentQuery.ru=Агентские запросы
dashboard.billing.filter.operationType.qaQuery.en=Quality evaluation
dashboard.billing.filter.operationType.qaQuery.ru=Оценка качества
dashboard.billing.filter.operationType.chunking.en=Chunking
dashboard.billing.filter.operationType.chunking.ru=Чанкинг
dashboard.billing.filter.operationType.vectorization.en=Vectorisation
dashboard.billing.filter.operationType.vectorization.ru=Векторизация
dashboard.billing.filter.operationType.storage.en=Data storage
dashboard.billing.filter.operationType.storage.ru=Хранение данных
dashboard.billing.filter.tokenType.input.en=Input
dashboard.billing.filter.tokenType.input.ru=В запросе
dashboard.billing.filter.tokenType.output.en=Output
dashboard.billing.filter.tokenType.output.ru=В ответе
