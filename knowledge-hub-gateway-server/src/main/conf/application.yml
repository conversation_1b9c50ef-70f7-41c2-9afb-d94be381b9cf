server:
  address: 0.0.0.0
  port: ${port:9333}
  hostname: khub.just-ai.com
  shutdown: graceful
  username: # system username
  password: # system password

spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    deserialization:
      fail-on-unknown-properties: false
  main:
    banner-mode: off
  application:
    name: knowledge-hub-gateway
  lifecycle:
    timeout-per-shutdown-phase: 100s
  pid:
    file: /opt/justai/knowledge-hub-gateway-be/bin/${id:application}.pid
    fail-on-write-error: true
  mvc:
    throw-exception-if-no-handler-found: true
  session:
    store-type: none
  messages:
    basename: messages/messages
    encoding: UTF-8
    default-locale: ru
    cache-duration: -1
  cloud:
    httpclientfactories:
      ok:
        enabled: false
    loadbalancer:
      justai:
        enabled: false
      enabled: false
      retry:
        enabled: true
      ai-vendor:
        balancing-strategy: round-robin
        retry:
          enabled: true
          safe-calls:
            methods: [ 'GET', 'DELETE' ]
            always-retry: false
            retryable-status-codes: [ 502, 503, 504 ]
            retry-on-exception: false
          not-safe-calls:
            always-retry: false
            retryable-status-codes: [ 502, 503, 504 ]
            retry-on-exception: false
      clients:
        knowledge-hub-rag:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        knowledge-hub-evaluator:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        knowledge-hub-parser:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        knowledge-hub-ingester:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin
        accountsadmin:
          configurations: ai-vendor
          ai-vendor:
            balancing-strategy: round-robin

  devtools:
    livereload:
      enabled: false
  datasource:
    url: *******************************************
    username:
    password:
    driver-class-name: org.postgresql.Driver
    validationQuery: SELECT 1
    test-on-borrow: true
    remove-abandoned: true
    remove-abandoned-timeout: 60
    test-while-idle: true
    hikari:
      maximumPoolSize: 50
      connectionTestQuery: SELECT 1
      #      Should be several seconds shorter than DB connection time limit
      maxLifeTime: 120000
      #      Should be less than maxLifeTime
      leakDetectionThreshold: 60000
  jpa:
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    generate-ddl: false
    properties:
      hibernate:
        utils:
          jackson:
            object:
              mapper: com.justai.khub.common.util.HibernateObjectMapperSupplier
        enable_lazy_load_no_trans: true
      javax:
        persistence:
          validation:
            mode: none # expect all validations to be applied in services that have access to spring beans

resilience4j:
  retry:
    configs:
      knowledge-hub-rag:
        max-attempts: 2
        enable-exponential-backoff: true
        exponential-backoff-multiplier: 2
        enable-randomized-wait: true
        wait-duration: 20ms # 20ms +- 10ms, 40ms +- 20ms
        # see io.github.resilience4j.common.retry.configuration.RetryConfigurationProperties.configureExponentialBackoffAndRandomizedWait
      knowledge-hub-ingester:
        base-config: knowledge-hub-rag
      knowledge-hub-parser:
        base-config: knowledge-hub-rag
      knowledge-hub-evaluator:
        base-config: knowledge-hub-rag
      accountsadmin:
        base-config: knowledge-hub-rag

session-db:
  host: localhost
  database: justaisessions
  username:
  password:

just-session:
  mongodb:
    uri: mongodb://localhost:27017/justaisessions
  account-user-compatibility: sessionInfo
  required-features:
  required-permissions:

eureka:
  client:
    enabled: false
    service-url:
      defaultZone: # Eureaka SD-endpoint url, for example http://localhost:9290/eureka
  instance:
    instance-id: khg01:local-${spring.application.name}:${server.port}
    statusPageUrlPath: /version
    healthCheckUrlPath: /version

management:
  metrics:
    distribution:
      slo:
        http.server.requests:
          - 200ms
          - 500ms
          - 1s
          - 10s
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  loggable-body-size: 2048 # Number of request/response body characters that will be printed.
  file:
    name: ${log.dir:/tmp}/knowledge-hub-gateway-be.main.log
    max-history: 90
    max-size: 100MB

local-features:
  chunksRetrieving: false
  channels: false
  customQAPromptsEnabled: false
  qaModuleEnabled: true
  tariffInfoEnabled: true
  minervaEnabled: true

integrations:
  checkIntegrationsJobPoolSize: 5
  checkErrorsLimit: 10
  lakefs:
    base-url: http://localhost:8009/api/v1
    access-key-id: TODO
    secret-access-key: TODO
    storage-namespace: TODO
    connectTimeout: 60000
    readTimeout: 120000
  rag:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3010
    username:
    password:
  aa:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:9140
    username:
    password:
  ingester:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3040
    username:
    password:
  evaluator:
    base-url: http://localhost:3020
    username:
    password:
  parser:
    eureka-enabled: ${eureka.client.enabled}
    base-url: http://localhost:3030
    username:
    password:
  external-sources:
    checkIntervalMinutes: 1
    downloadAttachments: true
    attachmentImageTypes:
      - png
      - jpg
      - jpeg
      - svg
      - gif
  confluence:
    maxPages: 1000
  minerva:
    maxPages: 1000
  jaicp:
    baseUrl: https://app.jaicp.com
    template-name: "khub-default-template-ru"
  atlassian:
    client-id: TODO
    client-secret: TODO
    redirect-url: https://${server.hostname}/api/khub/integrations/atlassian/oauth2
    # You can copy this string from atlassian app settings. Remember to add offline_access scope, otherwise refresh token will not be retrieved
    scope-string: read%3Aconfluence-space.summary%20read%3Aconfluence-props%20read%3Aconfluence-content.all%20read%3Aconfluence-content.summary%20search%3Aconfluence%20read%3Aconfluence-content.permission%20read%3Aconfluence-user%20read%3Aconfluence-groups%20readonly%3Acontent.attachment%3Aconfluence%20read%3Aspace%3Aconfluence%20offline_access
    auth-timeout-seconds: 180
  billing:
    enabled: false
    eureka-enabled: ${eureka.client.enabled}
    address: localhost:9185

internal-http-client:
  connectTimeout: 10_000
  readTimeout: 1200_000
  maxConnections: 800
  maxConnectionsPerRoute: 100
  connTimeToLive: 0
  validateAfterInactivity: 500
  disableSslVerifier: false
  trustStore:
  trustStorePassword:
  trustStoreType:

external-http-client:
  connectTimeout: 10_000
  readTimeout: 120_000
  maxConnections: 800
  maxConnectionsPerRoute: 100
  connTimeToLive: 0
  validateAfterInactivity: 500
  disableSslVerifier: false
  trustStore:
  trustStorePassword:
  trustStoreType:

api-keys:
  key-length: 60
  visible-key-length: 10
  request-samples:
    - content: |
        curl -H "Authorization: Bearer ${api_key}"
        https://${server.hostname}/api/knowledge-hub/info
      hint: 'apiKeys.request.sample.projectInfo.hint.ru'
    - content: |
        curl -d '{"query": "Что ты умеешь?"}'
        -H "Authorization: Bearer ${api_key}"
        -H "Content-Type: application/json"
        https://${server.hostname}/api/knowledge-hub/query
      hint: 'apiKeys.request.sample.generateQuery.hint.ru'

ingest:
  imageProcessor:
    model: "openai/gpt-4o"
    enabled: true
  enrichChunks: false
  ingestJobThreadPoolSize: 5
  fileProcessingThreadPoolSize: 4
  parser:
    parsingTimeoutSeconds: 1200
  chunker:
    llmChunkerEnabled: true
  vectorizer:
    options:
      - title: "text_embedding_3_large"
        value: "openai/text-embedding-3-large"
        img: "/static/images/openai.svg"
      - title: "multilingual_e5_large"
        value: "intfloat/multilingual-e5-large"
        img: "/static/images/internal-ner-model.svg"
  supportedExtensions:
    - csv
    - docx
    - html
    - json
    - md
    - mdx
    - pdf
    - txt
    - xml
    - yaml
    - yml
  maxFileSizeMB: 25
  maxCSVFilesCount: 10
  # Files with these extensions bypass parsing and go directly to knowledge-hub-ingester
  ingestOnlyExtensions:
      - csv
      - docx
      - json
      - md
      - mdx
      - txt
      - xml
      - yaml
      - yml
      - pdf
  maxActiveTasksPerAccount: 10

rag:
  csvProcessingEnabled: true
  chatQueryProcessingJobThreadPoolSize: 50
  llm:
    models:
      - title: "GPT-4o"
        value: "openai/gpt-4o"
        qaEnabled: true
        indexationEnabled: true
      - title: "GPT-4o-mini"
        value: "openai/gpt-4o-mini"
        qaEnabled: true
      - title: "Qwen2.5-32B-Instruct-fp8-dynamic"
        value: "vllm-Qwen2.5-32B-Instruct-fp8-dynamic"
        qaEnabled: true
        pipelineTypes:
          - "semantic"
      - title: "llama3.1-8b"
        value: "vllm-llama3.1-8b"
        qaEnabled: true
        pipelineTypes:
          - "semantic"
  reranker:
    model:
      value: "BAAI/bge-reranker-v2-m3"
      enabled: true
  imageSearchStrategy: add_parent

prompts:
  semanticGenerationResponseRulePattern: "- \\((?<ruleIndex>[0-9]+)\\) (?<ruleText>[^\\n\\r]*)"

billing:
  enabled: true
  transactionTimeoutSeconds: 3600
  overdraftStreakLimit: 30 # in days
  maxRetries: 5
#  list of account ids that must be billed per requests
  accountsWithPerRequestBilling:

attachments:
  enabled: true
  public-link-duration: "2h" # supported suffixes - ns, us, ms, s, m, h, d
  base-url: "http://my.storage.com/attachments"

jobs:
  timedOutEntitiesCheckIntervalSeconds: 7200
  entitiesProcessingTimeout: "2h"

qa:
  useChunksContent: false
  testSetGenerationThreadPoolSize: 4
  testSetRunThreadPoolSize: 4
  defaultEvaluationModel: "openai/gpt-4o"
  defaultGenerationModel: "openai/gpt-4o"
  schedulingTimezone: "Europe/Moscow"

rate-limit:
  # for now, we have only global bucket. No reason to expire it too often
  bucket-expired-after: PT24h
  remove-expired-buckets:
    enabled: true
    max-to-remove-in-one-transaction: 1000
    threshold-to-continue-removing: 50

license:
  key:
    client-public-key: null
    encoded-license-key: null

migration:
  rawConfiguration: {}

analytics:
  gtm: null

scheduling:
  billing:
    processVersionsWithOverdraftStreakIntervalSeconds: 18000 # every 5 hours
    billingRetryIntervalSeconds: 60
    # once a hour
    storageBillingCron: "0 0 * * * *"

project:
  maxProjectsPerAccount: 20

